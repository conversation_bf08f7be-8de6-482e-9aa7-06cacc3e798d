// src/lib/supabase-auth.ts
'use client';

import type { User } from './auth';
import { supabase } from './supabase';

export interface SupabaseAuthResponse {
  user: User | null;
  error: string | null;
  success: boolean;
}

export class SupabaseAuthService {
  /**
   * Sign up a new user with Supabase
   */
  static async signUp(email: string, password: string, userData: {
    name: string;
    role: string;
  }): Promise<SupabaseAuthResponse> {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: userData.name,
            role: userData.role
          }
        }
      });

      if (error) {

        // Provide more specific error messages based on error codes
        let userFriendlyMessage = error.message;

        if (error.message.includes('User already registered')) {
          userFriendlyMessage = 'An account with this email already exists. Please sign in instead.';
        } else if (error.message.includes('Password should be at least')) {
          userFriendlyMessage = 'Password must be at least 6 characters long.';
        } else if (error.message.includes('Invalid email')) {
          userFriendlyMessage = 'Please enter a valid email address.';
        } else if (error.message.includes('Signup is disabled')) {
          userFriendlyMessage = 'Account registration is currently disabled. Please contact support.';
        }

        return {
          user: null,
          error: userFriendlyMessage,
          success: false
        };
      }      if (data.user) {
        // Try to create profile in the profiles table, but handle gracefully if table doesn't exist
        try {
          const { error: profileError } = await supabase
            .from('profiles')
            .insert({
              id: data.user.id,
              email: data.user.email || '',
              full_name: userData.name,
              role: userData.role as 'student' | 'teacher' | 'admin' | 'parent'
            });

          if (profileError) {
            console.warn('Profile creation failed:', profileError);
          }
        } catch (profileError) {
          console.warn('Profiles table not accessible during signup:', profileError);
        }

        const user: User = {
          name: userData.name,
          email: data.user.email || '',
          role: userData.role,
          isAuthenticated: true
        };

        // Store in localStorage for compatibility
        this.storeUserLocally(user);

        return {
          user,
          error: null,
          success: true
        };
      }

      return {
        user: null,
        error: 'User creation failed',
        success: false
      };
    } catch (error) {
      return {
        user: null,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        success: false
      };
    }
  }

  /**
   * Sign in an existing user with Supabase
   */
  static async signIn(email: string, password: string): Promise<SupabaseAuthResponse> {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (error) {

        // Provide more specific error messages based on error codes
        let userFriendlyMessage = error.message;

        if (error.message.includes('Invalid login credentials')) {
          userFriendlyMessage = 'Invalid email or password. Please check your credentials and try again.';
        } else if (error.message.includes('Email not confirmed')) {
          userFriendlyMessage = 'Please check your email and click the confirmation link before signing in.';
        } else if (error.message.includes('Too many requests')) {
          userFriendlyMessage = 'Too many login attempts. Please wait a moment before trying again.';
        } else if (error.message.includes('User not found')) {
          userFriendlyMessage = 'No account found with this email address. Please sign up first.';
        } else if (error.message.includes('fetch')) {
          userFriendlyMessage = 'Network error. Please check your internet connection and try again.';
        }

        return {
          user: null,
          error: userFriendlyMessage,
          success: false
        };
      }

      if (data.user) {        // Try to get user profile from database, but don't fail if table doesn't exist
        let profile = null;
        try {
          const { data: profileData, error: profileError } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', data.user.id)
            .single();

          if (!profileError) {
            profile = profileData;
          }
        } catch (error) {
          // Profiles table might not exist yet, continue without it
          console.log('Profile lookup failed, using metadata:', error);
        }

        const userName = profile?.full_name || data.user.user_metadata?.full_name || data.user.email?.split('@')[0] || '';
        const userRole = profile?.role || data.user.user_metadata?.role || 'student';

        const user: User = {
          name: userName,
          email: data.user.email || '',
          role: userRole,
          isAuthenticated: true
        };

        // Store in localStorage for compatibility
        this.storeUserLocally(user);

        return {
          user,
          error: null,
          success: true
        };
      }

      return {
        user: null,
        error: 'Sign in failed',
        success: false
      };
    } catch (error) {
      return {
        user: null,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        success: false
      };
    }
  }

  /**
   * Sign in with Google OAuth
   */  static async signInWithGoogle(): Promise<SupabaseAuthResponse> {
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/dashboard`
        }
      });

      if (error) {
        return {
          user: null,
          error: error.message,
          success: false
        };
      }

      return {
        user: null,
        error: null,
        success: true
      };
    } catch (error) {
      return {
        user: null,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        success: false
      };
    }
  }

  /**
   * Sign in with Facebook OAuth
   */  static async signInWithFacebook(): Promise<SupabaseAuthResponse> {
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'facebook',
        options: {
          redirectTo: `${window.location.origin}/dashboard`
        }
      });

      if (error) {
        return {
          user: null,
          error: error.message,
          success: false
        };
      }

      return {
        user: null,
        error: null,
        success: true
      };
    } catch (error) {
      return {
        user: null,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        success: false
      };
    }
  }

  /**
   * Sign out the current user
   */
  static async signOut(): Promise<void> {
    try {
      await supabase.auth.signOut();
      this.clearUserLocally();
    } catch (error) {
      console.error('Sign out error:', error);
      // Clear local storage even if Supabase signOut fails
      this.clearUserLocally();
    }
  }

  /**
   * Get the current authenticated user
   */
  static async getCurrentUser(): Promise<User | null> {
    try {
      // Create a timeout promise to prevent hanging (reduced to 2 seconds)
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(new Error('getCurrentUser timeout'));
        }, 2000);
      });

      // Create the session check promise
      const sessionPromise = supabase.auth.getSession();

      // Race between session check and timeout
      const { data: { session }, error: sessionError } = await Promise.race([
        sessionPromise,
        timeoutPromise
      ]);

      if (sessionError) {
        // Return null on any session error to trigger fallback
        return null;
      }

      if (!session?.user) {
        // No session found, return null
        return null;
      }

      const user = session.user;

      // Use metadata and fallbacks, skip database lookup to avoid hanging
      const userName = user.user_metadata?.full_name || user.email?.split('@')[0] || 'User';
      const userRole = user.user_metadata?.role || 'student';

      const authUser: User = {
        name: userName,
        email: user.email || '',
        role: userRole,
        isAuthenticated: true
      };

      // Store in localStorage for compatibility
      this.storeUserLocally(authUser);

      return authUser;
    } catch (error) {
      // On any error (including timeout), try localStorage fallback
      if (typeof window !== 'undefined') {
        const localAuth = localStorage.getItem('isAuthenticated');
        if (localAuth === 'true') {
          const fallbackUser: User = {
            name: localStorage.getItem('userName') || 'User',
            email: localStorage.getItem('userEmail') || '',
            role: localStorage.getItem('userRole') || 'student',
            isAuthenticated: true
          };
          return fallbackUser;
        }
      }

      // If all else fails, return null to trigger redirect to /product
      return null;
    }
  }

  /**
   * Check if user session exists
   */
  static async getSession() {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      return { session, error };
    } catch (error) {
      console.error('Get session error:', error);
      return { session: null, error };
    }
  }

  /**
   * Listen to auth state changes
   */
  static onAuthStateChange(callback: (user: User | null) => void) {
    return supabase.auth.onAuthStateChange(async (event, session) => {
      if (session?.user) {
        const user = await this.getCurrentUser();
        callback(user);
      } else {
        this.clearUserLocally();
        callback(null);
      }
    });
  }

  /**
   * Store user data in localStorage for compatibility with existing code
   */
  private static storeUserLocally(user: User): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem('isAuthenticated', 'true');
      localStorage.setItem('userName', user.name);
      localStorage.setItem('userEmail', user.email);
      localStorage.setItem('userRole', user.role);
    }
  }

  /**
   * Clear user data from localStorage
   */
  private static clearUserLocally(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('isAuthenticated');
      localStorage.removeItem('userName');
      localStorage.removeItem('userEmail');
      localStorage.removeItem('userRole');
    }
  }

  /**
   * Test Supabase connection
   */
  static async testConnection(): Promise<{ success: boolean; error?: string }> {
    try {
      const { data, error } = await supabase.auth.getSession();
      console.log('Supabase connection test:', { data, error });

      if (error) {
        return {
          success: false,
          error: `Connection failed: ${error.message}`
        };
      }

      return {
        success: true
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown connection error'
      };
    }
  }
}
