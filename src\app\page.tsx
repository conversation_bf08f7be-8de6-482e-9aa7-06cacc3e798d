'use client';

import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { useAuth } from '../components/auth/auth-provider';

export default function HomePage() {
  const router = useRouter();
  const { user, loading } = useAuth();

  useEffect(() => {
    console.log('🏠 HomePage: Auth state:', { user, loading });

    // Don't do anything while still loading
    if (loading) {
      console.log('⏳ HomePage: Still loading auth...');
      return;
    }

    // Once loading is complete, redirect based on auth state
    console.log('🔍 HomePage: Auth loading complete, checking user state...');

    if (user && user.isAuthenticated) {
      console.log('✅ HomePage: User authenticated, redirecting to dashboard');
      router.push('/dashboard');
    } else {
      console.log('📭 HomePage: No user, redirecting to product page');
      router.push('/product');
    }
  }, [router, user, loading]);

  // Show loading screen while auth is being determined
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-white text-slate-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-slate-600 mb-2">Loading EduPro...</p>
          <p className="text-xs text-slate-400 mb-4">Checking authentication status</p>

          {/* Bypass link for testing */}
          <div className="mt-6">
            <button
              onClick={() => router.push('/product')}
              className="text-indigo-600 hover:text-indigo-700 underline text-sm"
            >
              Continue to Product Page →
            </button>
          </div>
        </div>
      </div>
    );
  }

  // This should not be reached as useEffect will handle redirects
  return null;
}
