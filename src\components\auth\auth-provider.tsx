// src/components/auth/auth-provider.tsx
'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { User } from '../../lib/auth';
import { SupabaseAuthService } from '../../lib/supabase-auth';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  error: string | null;
  signIn: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  signUp: (email: string, password: string, userData: { name: string; role: string }) => Promise<{ success: boolean; error?: string }>;
  signOut: () => Promise<void>;
  signInWithGoogle: () => Promise<{ success: boolean; error?: string }>;
  signInWithFacebook: () => Promise<{ success: boolean; error?: string }>;
  clearError: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    console.log('🔄 AuthProvider: Initializing auth state...');

    let isMounted = true;
    let subscription: any = null;

    const initializeAuth = async () => {
      try {
        console.log('🔍 AuthProvider: Getting current user...');

        // Add timeout to prevent hanging
        const timeoutPromise = new Promise<User | null>((_, reject) => {
          setTimeout(() => reject(new Error('Auth initialization timeout')), 5000);
        });

        const authPromise = SupabaseAuthService.getCurrentUser();

        const currentUser = await Promise.race([authPromise, timeoutPromise]);

        if (isMounted) {
          console.log('👤 AuthProvider: Current user result:', currentUser);
          setUser(currentUser);
          setError(null);
          setLoading(false);
        }
      } catch (error) {
        console.error('❌ AuthProvider: Failed to initialize auth:', error);
        if (isMounted) {
          setUser(null);
          setError(error instanceof Error ? error.message : 'Authentication failed');
          setLoading(false);
        }
      }
    };

    // Set up auth state listener
    const setupAuthListener = () => {
      try {
        const { data } = SupabaseAuthService.onAuthStateChange((user) => {
          console.log('🔄 AuthProvider: Auth state changed:', user);
          if (isMounted) {
            setUser(user);
            setLoading(false);
          }
        });
        subscription = data?.subscription;
      } catch (error) {
        console.error('❌ AuthProvider: Failed to set up auth listener:', error);
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    // Initialize auth and set up listener
    initializeAuth();
    setupAuthListener();

    return () => {
      console.log('🧹 AuthProvider: Cleaning up...');
      isMounted = false;
      subscription?.unsubscribe();
    };
  }, []);

  const signIn = async (email: string, password: string) => {
    const result = await SupabaseAuthService.signIn(email, password);
    if (result.success && result.user) {
      setUser(result.user);
    }
    return { success: result.success, error: result.error || undefined };
  };

  const signUp = async (email: string, password: string, userData: { name: string; role: string }) => {
    const result = await SupabaseAuthService.signUp(email, password, userData);
    if (result.success && result.user) {
      setUser(result.user);
    }
    return { success: result.success, error: result.error || undefined };
  };

  const signOut = async () => {
    await SupabaseAuthService.signOut();
    setUser(null);
  };

  const signInWithGoogle = async () => {
    const result = await SupabaseAuthService.signInWithGoogle();
    return { success: result.success, error: result.error || undefined };
  };

  const signInWithFacebook = async () => {
    const result = await SupabaseAuthService.signInWithFacebook();
    return { success: result.success, error: result.error || undefined };
  };

  const clearError = () => {
    setError(null);
  };

  const value: AuthContextType = {
    user,
    loading,
    error,
    signIn,
    signUp,
    signOut,
    signInWithGoogle,
    signInWithFacebook,
    clearError,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
