// src/components/auth/auth-provider.tsx
'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { User } from '../../lib/auth';
import { SupabaseAuthService } from '../../lib/supabase-auth';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  error: string | null;
  signIn: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  signUp: (email: string, password: string, userData: { name: string; role: string }) => Promise<{ success: boolean; error?: string }>;
  signOut: () => Promise<void>;
  signInWithGoogle: () => Promise<{ success: boolean; error?: string }>;
  signInWithFacebook: () => Promise<{ success: boolean; error?: string }>;
  clearError: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let isMounted = true;
    let subscription: any = null;
    let timeoutId: NodeJS.Timeout;

    // Force loading to false after maximum timeout to prevent infinite loading
    const forceLoadingComplete = () => {
      if (isMounted) {
        setLoading(false);
        setUser(null);
        setError(null); // Don't show error, just redirect
      }
    };

    const initializeAuth = async () => {
      try {
        // Set absolute maximum timeout of 5 seconds
        timeoutId = setTimeout(forceLoadingComplete, 5000);

        // Create timeout promise that rejects after 3 seconds
        const timeoutPromise = new Promise<never>((_, reject) => {
          setTimeout(() => reject(new Error('Auth check timeout')), 3000);
        });

        const authPromise = SupabaseAuthService.getCurrentUser();

        const currentUser = await Promise.race([authPromise, timeoutPromise]);

        if (isMounted) {
          clearTimeout(timeoutId);
          setUser(currentUser);
          setError(null);
          setLoading(false);
        }
      } catch (error) {
        if (isMounted) {
          clearTimeout(timeoutId);
          setUser(null);
          setError(null); // Don't show error, just redirect to product page
          setLoading(false);
        }
      }
    };

    // Set up auth state listener with error handling
    const setupAuthListener = () => {
      try {
        const { data } = SupabaseAuthService.onAuthStateChange((user) => {
          if (isMounted) {
            setUser(user);
            if (loading) {
              setLoading(false);
            }
          }
        });
        subscription = data?.subscription;
      } catch (error) {
        // Silently fail and ensure loading is false
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    // Start initialization
    initializeAuth();
    setupAuthListener();

    return () => {
      isMounted = false;
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      subscription?.unsubscribe();
    };
  }, []);

  const signIn = async (email: string, password: string) => {
    const result = await SupabaseAuthService.signIn(email, password);
    if (result.success && result.user) {
      setUser(result.user);
    }
    return { success: result.success, error: result.error || undefined };
  };

  const signUp = async (email: string, password: string, userData: { name: string; role: string }) => {
    const result = await SupabaseAuthService.signUp(email, password, userData);
    if (result.success && result.user) {
      setUser(result.user);
    }
    return { success: result.success, error: result.error || undefined };
  };

  const signOut = async () => {
    try {
      await SupabaseAuthService.signOut();
      setUser(null);
      setError(null);
    } catch (error) {
      // Even if Supabase signOut fails, clear local state
      setUser(null);
      setError(null);
    }
  };

  const signInWithGoogle = async () => {
    const result = await SupabaseAuthService.signInWithGoogle();
    return { success: result.success, error: result.error || undefined };
  };

  const signInWithFacebook = async () => {
    const result = await SupabaseAuthService.signInWithFacebook();
    return { success: result.success, error: result.error || undefined };
  };

  const clearError = () => {
    setError(null);
  };

  const value: AuthContextType = {
    user,
    loading,
    error,
    signIn,
    signUp,
    signOut,
    signInWithGoogle,
    signInWithFacebook,
    clearError,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
