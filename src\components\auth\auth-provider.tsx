// src/components/auth/auth-provider.tsx
'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { User } from '../../lib/auth';
import { SupabaseAuthService } from '../../lib/supabase-auth';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  signUp: (email: string, password: string, userData: { name: string; role: string }) => Promise<{ success: boolean; error?: string }>;
  signOut: () => Promise<void>;
  signInWithGoogle: () => Promise<{ success: boolean; error?: string }>;
  signInWithFacebook: () => Promise<{ success: boolean; error?: string }>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    console.log('🔄 AuthProvider: Initializing auth state...');

    // Initialize auth state with shorter timeout
    const initializeAuth = async () => {
      try {
        console.log('🔍 AuthProvider: Getting current user...');
        const currentUser = await SupabaseAuthService.getCurrentUser();
        console.log('👤 AuthProvider: Current user result:', currentUser);
        setUser(currentUser);
      } catch (error) {
        console.error('❌ AuthProvider: Failed to initialize auth:', error);
        setUser(null); // Set to null on error
      } finally {
        console.log('✅ AuthProvider: Setting loading to false');
        setLoading(false);
      }
    };

    // Set a shorter timeout to prevent infinite loading
    const timeoutId = setTimeout(() => {
      console.warn('⏰ AuthProvider: Auth initialization timeout - forcing loading to false');
      setLoading(false);
      setUser(null); // Assume no user on timeout
    }, 3000); // 3 second timeout (reduced from 10)

    // Start initialization
    initializeAuth().then(() => {
      console.log('🎯 AuthProvider: Auth initialization completed');
      clearTimeout(timeoutId);
    }).catch((error) => {
      console.error('💥 AuthProvider: Auth initialization failed:', error);
      clearTimeout(timeoutId);
      setLoading(false);
      setUser(null);
    });

    // Listen for auth changes
    let subscription: any = null;
    try {
      const { data } = SupabaseAuthService.onAuthStateChange((user) => {
        console.log('🔄 AuthProvider: Auth state changed:', user);
        setUser(user);
        setLoading(false);
      });
      subscription = data?.subscription;
    } catch (error) {
      console.error('❌ AuthProvider: Failed to set up auth listener:', error);
    }

    return () => {
      console.log('🧹 AuthProvider: Cleaning up...');
      clearTimeout(timeoutId);
      subscription?.unsubscribe();
    };
  }, []);

  const signIn = async (email: string, password: string) => {
    const result = await SupabaseAuthService.signIn(email, password);
    if (result.success && result.user) {
      setUser(result.user);
    }
    return { success: result.success, error: result.error || undefined };
  };

  const signUp = async (email: string, password: string, userData: { name: string; role: string }) => {
    const result = await SupabaseAuthService.signUp(email, password, userData);
    if (result.success && result.user) {
      setUser(result.user);
    }
    return { success: result.success, error: result.error || undefined };
  };

  const signOut = async () => {
    await SupabaseAuthService.signOut();
    setUser(null);
  };

  const signInWithGoogle = async () => {
    const result = await SupabaseAuthService.signInWithGoogle();
    return { success: result.success, error: result.error || undefined };
  };

  const signInWithFacebook = async () => {
    const result = await SupabaseAuthService.signInWithFacebook();
    return { success: result.success, error: result.error || undefined };
  };

  const value: AuthContextType = {
    user,
    loading,
    signIn,
    signUp,
    signOut,
    signInWithGoogle,
    signInWithFacebook,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
